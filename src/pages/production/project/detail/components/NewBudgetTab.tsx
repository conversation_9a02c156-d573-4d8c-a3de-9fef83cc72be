import { FolderAddOutlined, PlusOutlined, MoreOutlined } from '@ant-design/icons'
import { Button, Card, Divider, Dropdown, Empty, Flex, List, message, Popconfirm, Space, Tag, Typography } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import {
  BudgetCategory,
  BudgetSubcategory,
  getBudgetCategoryLabel,
  getBudgetSubcategoryLabel
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem, IPrProductionItemsDetail } from '../store'
import { ListHeader } from '@fe/rockrose'

const { Text } = Typography

interface INewBudgetTabProps {
  productionId: number
  project?: IProductionListItem
}

// 扁平化的预算项目接口（用于列表展示）
interface IFlatBudgetItem extends IPrProductionItemsDetail {
  budgetItemIndex: number // 对应的BudgetItem索引
  budgetItemId?: number // 对应的BudgetItem ID
  itemName?: string // 打包名称
  isPackage?: boolean // 是否打包
  quotedPrice?: number // 单价
  dayCount?: number // 拍摄天数
  totalPrice?: number // 总金额
  hasInvoice?: number // 是否正规发票
  description?: string // 备注说明
}

// 二级渲染数据接口（按照excel结构）
interface IGroupedBudgetData {
  category: BudgetCategory
  categoryLabel: string
  subcategories: {
    subcategory: BudgetSubcategory
    subcategoryLabel: string
    items: IFlatBudgetItem[]
    subtotal: number
  }[]
  total: number
}

const NewBudgetTab: React.FC<INewBudgetTabProps> = ({ productionId, project }) => {
  const [budgetItems, setBudgetItems] = useState<IPrProductionBudgetItem[]>([])
  const [loading, setLoading] = useState(false)
  const [isAddPackageModalOpen, setIsAddPackageModalOpen] = useState(false)
  const [isAddExpenseModalOpen, setIsAddExpenseModalOpen] = useState(false)

  const { getBudgetItems, deleteBudgetItems } = useProjectDetailStore()

  useEffect(() => {
    if (productionId) {
      loadBudgetItems()
    }
  }, [productionId])

  // 加载预算项目列表
  const loadBudgetItems = async () => {
    setLoading(true)
    try {
      const result = await getBudgetItems({ productionId })
      setBudgetItems(result || [])
    } catch (error) {
      message.error('加载预算项目失败')
    } finally {
      setLoading(false)
    }
  }

  // 使用useMemo计算三份数据
  const { flatBudgetItems, groupedBudgetData } = useMemo(() => {
    // 1. 扁平化的预算项目数组（用于列表展示）
    const flatItems: IFlatBudgetItem[] = []
    
    budgetItems.forEach((budgetItem, budgetItemIndex) => {
      if (budgetItem.isPackage && budgetItem.itemsDetail?.length) {
        // 打包项目：展开itemsDetail
        budgetItem.itemsDetail.forEach(detail => {
          flatItems.push({
            ...detail,
            budgetItemIndex,
            budgetItemId: budgetItem.id,
            itemName: budgetItem.itemName,
            isPackage: budgetItem.isPackage,
            quotedPrice: budgetItem.quotedPrice,
            dayCount: budgetItem.dayCount,
            totalPrice: budgetItem.totalPrice,
            hasInvoice: budgetItem.hasInvoice,
            description: budgetItem.description,
          })
        })
      } else {
        // 非打包项目：直接添加（需要构造一个虚拟的detail）
        flatItems.push({
          id: budgetItem.id,
          itemId: budgetItem.id || 0,
          category: BudgetCategory.MISCELLANEOUS, // 默认分类
          subcategory: BudgetSubcategory.MISCELLANEOUS_DRAMA_EXPENSES, // 默认子分类
          personCount: budgetItem.personCount,
          budgetItemIndex,
          budgetItemId: budgetItem.id,
          itemName: budgetItem.itemName,
          isPackage: budgetItem.isPackage,
          quotedPrice: budgetItem.quotedPrice,
          dayCount: budgetItem.dayCount,
          totalPrice: budgetItem.totalPrice,
          hasInvoice: budgetItem.hasInvoice,
          description: budgetItem.description,
          categoryLabel: getBudgetCategoryLabel(BudgetCategory.MISCELLANEOUS),
          subcategoryLabel: getBudgetSubcategoryLabel(BudgetSubcategory.MISCELLANEOUS_DRAMA_EXPENSES),
        })
      }
    })

    // 2. BudgetItem的map（用于索引阅读）
    const itemsMap = new Map<number, IPrProductionBudgetItem>()
    budgetItems.forEach((item, index) => {
      if (item.id) {
        itemsMap.set(item.id, item)
      }
      // 也可以用索引作为key
      itemsMap.set(index, item)
    })

    // 3. 按照excel结构的二级渲染数据
    const grouped: IGroupedBudgetData[] = []
    const categoryMap = new Map<BudgetCategory, IGroupedBudgetData>()

    flatItems.forEach(item => {
      const { category, subcategory } = item
      
      // 获取或创建分类组
      let categoryGroup = categoryMap.get(category)
      if (!categoryGroup) {
        categoryGroup = {
          category,
          categoryLabel: getBudgetCategoryLabel(category),
          subcategories: [],
          total: 0
        }
        categoryMap.set(category, categoryGroup)
        grouped.push(categoryGroup)
      }

      // 查找或创建子分类组
      let subcategoryGroup = categoryGroup.subcategories.find(sub => sub.subcategory === subcategory)
      if (!subcategoryGroup) {
        subcategoryGroup = {
          subcategory,
          subcategoryLabel: getBudgetSubcategoryLabel(subcategory),
          items: [],
          subtotal: 0
        }
        categoryGroup.subcategories.push(subcategoryGroup)
      }

      // 添加项目到子分类组
      subcategoryGroup.items.push(item)
      
      // 计算金额
      const itemTotal = item.totalPrice || (item.quotedPrice || 0) * item.personCount * (item.dayCount || 1)
      subcategoryGroup.subtotal += itemTotal
      categoryGroup.total += itemTotal
    })

    return {
      flatBudgetItems: flatItems,
      budgetItemsMap: itemsMap,
      groupedBudgetData: grouped
    }
  }, [budgetItems])

  // 处理添加打包项目
  const handleAddPackage = () => {
    setIsAddPackageModalOpen(true)
  }

  // 处理添加费用项
  const handleAddExpense = () => {
    setIsAddExpenseModalOpen(true)
  }

  // 处理删除预算项目
  const handleDeleteBudgetItem = async (budgetItemId: number) => {
    try {
      const success = await deleteBudgetItems(budgetItemId)
      if (success) {
        message.success('删除成功')
        await loadBudgetItems()
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  // 计算总预算
  const totalBudget = useMemo(() => {
    return groupedBudgetData.reduce((total, group) => total + group.total, 0)
  }, [groupedBudgetData])

  // 渲染预算项目卡片
  const renderBudgetCard = (item: IFlatBudgetItem) => {
    const itemTotal = item.totalPrice || (item.quotedPrice || 0) * item.personCount * (item.dayCount || 1)
    
    const dropdownItems = [
      {
        key: 'edit',
        label: '编辑',
        onClick: () => {
          // TODO: 实现编辑功能
          message.info('编辑功能待实现')
        },
      },
      {
        key: 'delete',
        label: (
          <Popconfirm
            title="警告"
            description="确定要删除该预算项目吗？"
            onConfirm={() => item.budgetItemId && handleDeleteBudgetItem(item.budgetItemId)}
            okText="确定删除"
            cancelText="取消">
            <Typography.Text type="danger" style={{ width: '100%', display: 'inline-block' }}>
              删除
            </Typography.Text>
          </Popconfirm>
        ),
      },
    ]

    return (
      <List.Item>
        <Card size="small" className="full-h hover-move">
          <Flex vertical gap={6}>
            <Flex justify="space-between">
              <Space size={0} split={<Divider type="vertical" />}>
                <Text strong>{item.itemName || item.subcategoryLabel}</Text>
                {item.isPackage && <Tag color="purple">打包</Tag>}
                {item.hasInvoice ? <Text>带发票</Text> : <Text type="secondary">无发票</Text>}
                {item.quotedPrice != null && (
                  <Text>{`${project?.currencySymbol || '¥'}${item.quotedPrice.toLocaleString()}`}</Text>
                )}
                {item.personCount > 0 && <Text>数量: {item.personCount}</Text>}
                {item.dayCount && item.dayCount > 0 && <Text>天数: {item.dayCount}</Text>}
                {itemTotal > 0 && (
                  <Text type="danger">{`${project?.currencySymbol || '¥'}${itemTotal.toLocaleString()}`}</Text>
                )}
              </Space>
              <Dropdown menu={{ items: dropdownItems }} trigger={['click']} placement="bottomRight">
                <Button type="text" icon={<MoreOutlined />} />
              </Dropdown>
            </Flex>
            {item.description && <Typography.Text type="secondary">{item.description}</Typography.Text>}
          </Flex>
        </Card>
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader
        title={
          <Space>
            <span>新预算总计</span>
            <Text type="danger" strong className="fs-lg">
              {`${project?.currencySymbol || '¥'}`}
              {totalBudget.toLocaleString()}
            </Text>
          </Space>
        }>
        <Space>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<FolderAddOutlined />}
            onClick={handleAddPackage}>
            添加打包
          </Button>
          <Button 
            type="primary" 
            ghost 
            shape="round" 
            icon={<PlusOutlined />} 
            onClick={handleAddExpense}>
            添加费用
          </Button>
        </Space>
      </ListHeader>

      {flatBudgetItems.length > 0 ? (
        <List
          loading={loading}
          dataSource={flatBudgetItems}
          renderItem={renderBudgetCard}
          className="list-sm"
          split={false}
        />
      ) : (
        <Empty description="暂无预算数据" />
      )}

      {/* TODO: 添加模态框组件 */}
      {/* {isAddPackageModalOpen && (
        <AddPackageBudgetModal
          open={isAddPackageModalOpen}
          onCancel={() => setIsAddPackageModalOpen(false)}
          onSuccess={() => {
            setIsAddPackageModalOpen(false)
            loadBudgetItems()
          }}
          productionId={productionId}
        />
      )}

      {isAddExpenseModalOpen && (
        <AddExpenseModal
          open={isAddExpenseModalOpen}
          onCancel={() => setIsAddExpenseModalOpen(false)}
          onSuccess={() => {
            setIsAddExpenseModalOpen(false)
            loadBudgetItems()
          }}
          productionId={productionId}
        />
      )} */}
    </Flex>
  )
}

export default NewBudgetTab
